<?php
/**
 * Template Manager Class
 *
 * Manages business application templates for one-click installation
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class DAB_Template_Manager {

    /**
     * Initialize the Template Manager
     */
    public static function init() {
        add_action('wp_ajax_dab_install_template', array(__CLASS__, 'install_template'));
        add_action('wp_ajax_dab_get_template_preview', array(__CLASS__, 'get_template_preview'));
        add_action('wp_ajax_dab_search_templates', array(__CLASS__, 'search_templates'));
        add_action('wp_ajax_dab_delete_template_installation', array(__CLASS__, 'delete_template_installation'));

        // Add admin menu
        add_action('admin_menu', array(__CLASS__, 'add_admin_menu'));

        // Enqueue scripts
        add_action('admin_enqueue_scripts', array(__CLASS__, 'enqueue_scripts'));
    }

    /**
     * Create database tables for templates
     */
    public static function create_tables() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        // Templates table
        $templates_table = $wpdb->prefix . 'dab_app_templates';
        $sql_templates = "CREATE TABLE IF NOT EXISTS $templates_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            template_key VARCHAR(100) NOT NULL UNIQUE,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            category VARCHAR(100) NOT NULL,
            subcategory VARCHAR(100),
            icon VARCHAR(100),
            preview_image VARCHAR(255),
            template_config LONGTEXT NOT NULL,
            sample_data LONGTEXT,
            requirements LONGTEXT,
            version VARCHAR(20) DEFAULT '1.0.0',
            is_system TINYINT(1) DEFAULT 1,
            is_active TINYINT(1) DEFAULT 1,
            install_count INT DEFAULT 0,
            rating DECIMAL(3,2) DEFAULT 0.00,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_template_key (template_key),
            KEY idx_category (category),
            KEY idx_is_active (is_active),
            KEY idx_is_system (is_system)
        ) $charset_collate;";

        // Template installations table
        $installations_table = $wpdb->prefix . 'dab_template_installations';
        $sql_installations = "CREATE TABLE IF NOT EXISTS $installations_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            template_id BIGINT(20) UNSIGNED NOT NULL,
            template_key VARCHAR(100) NOT NULL,
            installation_name VARCHAR(255) NOT NULL,
            installed_tables LONGTEXT,
            installed_forms LONGTEXT,
            installed_views LONGTEXT,
            installed_workflows LONGTEXT,
            installation_data LONGTEXT,
            status VARCHAR(50) DEFAULT 'active',
            installed_by BIGINT(20) UNSIGNED,
            installed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_template_id (template_id),
            KEY idx_template_key (template_key),
            KEY idx_status (status),
            KEY idx_installed_by (installed_by)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_templates);
        dbDelta($sql_installations);

        // Insert default templates
        self::insert_default_templates();
    }

    /**
     * Add admin menu for template management
     */
    public static function add_admin_menu() {
        add_submenu_page(
            'dab_data_management',
            __('App Templates', 'db-app-builder'),
            __('App Templates', 'db-app-builder'),
            'manage_options',
            'dab_app_templates',
            array(__CLASS__, 'render_templates_page')
        );
    }

    /**
     * Enqueue scripts and styles
     */
    public static function enqueue_scripts($hook) {
        if (strpos($hook, 'dab_app_templates') !== false) {
            wp_enqueue_script(
                'dab-templates',
                DAB_PLUGIN_URL . 'assets/js/templates.js',
                array('jquery'),
                DAB_VERSION,
                true
            );

            wp_enqueue_style(
                'dab-templates',
                DAB_PLUGIN_URL . 'assets/css/templates.css',
                array(),
                DAB_VERSION
            );

            wp_localize_script('dab-templates', 'dab_templates', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('dab_templates_nonce'),
                'messages' => array(
                    'installing' => __('Installing template...', 'db-app-builder'),
                    'install_success' => __('Template installed successfully!', 'db-app-builder'),
                    'install_error' => __('Failed to install template. Please try again.', 'db-app-builder'),
                    'confirm_delete' => __('Are you sure you want to delete this template installation? This action cannot be undone.', 'db-app-builder')
                )
            ));
        }
    }

    /**
     * Render templates page
     */
    public static function render_templates_page() {
        global $wpdb;

        // Get template categories
        $templates_table = $wpdb->prefix . 'dab_app_templates';
        $categories = $wpdb->get_results(
            "SELECT DISTINCT category, COUNT(*) as template_count
             FROM $templates_table
             WHERE is_active = 1
             GROUP BY category
             ORDER BY category"
        );

        // Get all templates
        $templates = $wpdb->get_results(
            "SELECT * FROM $templates_table
             WHERE is_active = 1
             ORDER BY category, name"
        );

        // Get installed templates
        $installations_table = $wpdb->prefix . 'dab_template_installations';
        $installed_templates = $wpdb->get_results(
            "SELECT * FROM $installations_table
             WHERE status = 'active'
             ORDER BY installed_at DESC"
        );

        include plugin_dir_path(__FILE__) . '../admin/page-templates.php';
    }

    /**
     * Install a template via AJAX
     */
    public static function install_template() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'dab_templates_nonce')) {
            wp_send_json_error(__('Security check failed', 'db-app-builder'));
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'db-app-builder'));
        }

        $template_id = intval($_POST['template_id']);
        $installation_name = sanitize_text_field($_POST['installation_name']);

        if (empty($template_id) || empty($installation_name)) {
            wp_send_json_error(__('Template ID and installation name are required', 'db-app-builder'));
        }

        // Get template data
        global $wpdb;
        $templates_table = $wpdb->prefix . 'dab_app_templates';
        $template = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $templates_table WHERE id = %d AND is_active = 1",
            $template_id
        ));

        if (!$template) {
            wp_send_json_error(__('Template not found', 'db-app-builder'));
        }

        // Install the template
        $result = self::process_template_installation($template, $installation_name);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }

        // Update install count
        $wpdb->query($wpdb->prepare(
            "UPDATE $templates_table SET install_count = install_count + 1 WHERE id = %d",
            $template_id
        ));

        wp_send_json_success(array(
            'message' => __('Template installed successfully!', 'db-app-builder'),
            'installation_id' => $result['installation_id'],
            'redirect_url' => admin_url('admin.php?page=dab_tables')
        ));
    }

    /**
     * Process template installation
     */
    private static function process_template_installation($template, $installation_name) {
        global $wpdb;

        $template_config = json_decode($template->template_config, true);
        if (!$template_config) {
            return new WP_Error('invalid_config', __('Invalid template configuration', 'db-app-builder'));
        }

        $installation_data = array(
            'tables' => array(),
            'forms' => array(),
            'views' => array(),
            'workflows' => array()
        );

        try {
            // Start transaction
            $wpdb->query('START TRANSACTION');

            // Install tables and fields
            if (isset($template_config['tables'])) {
                foreach ($template_config['tables'] as $table_config) {
                    $table_result = self::install_template_table($table_config, $installation_name);
                    if (is_wp_error($table_result)) {
                        throw new Exception($table_result->get_error_message());
                    }
                    $installation_data['tables'][] = $table_result;
                }
            }

            // Install forms
            if (isset($template_config['forms'])) {
                foreach ($template_config['forms'] as $form_config) {
                    $form_result = self::install_template_form($form_config, $installation_data['tables']);
                    if (is_wp_error($form_result)) {
                        throw new Exception($form_result->get_error_message());
                    }
                    $installation_data['forms'][] = $form_result;
                }
            }

            // Install views
            if (isset($template_config['views'])) {
                foreach ($template_config['views'] as $view_config) {
                    $view_result = self::install_template_view($view_config, $installation_data['tables']);
                    if (is_wp_error($view_result)) {
                        throw new Exception($view_result->get_error_message());
                    }
                    $installation_data['views'][] = $view_result;
                }
            }

            // Record installation
            $installations_table = $wpdb->prefix . 'dab_template_installations';
            $wpdb->insert($installations_table, array(
                'template_id' => $template->id,
                'template_key' => $template->template_key,
                'installation_name' => $installation_name,
                'installed_tables' => json_encode($installation_data['tables']),
                'installed_forms' => json_encode($installation_data['forms']),
                'installed_views' => json_encode($installation_data['views']),
                'installed_workflows' => json_encode($installation_data['workflows']),
                'installation_data' => json_encode($installation_data),
                'installed_by' => get_current_user_id()
            ));

            $installation_id = $wpdb->insert_id;

            // Commit transaction
            $wpdb->query('COMMIT');

            return array(
                'installation_id' => $installation_id,
                'data' => $installation_data
            );

        } catch (Exception $e) {
            // Rollback transaction
            $wpdb->query('ROLLBACK');
            return new WP_Error('installation_failed', $e->getMessage());
        }
    }

    /**
     * Install template table
     */
    private static function install_template_table($table_config, $installation_name) {
        global $wpdb;

        $tables_table = $wpdb->prefix . 'dab_tables';
        $fields_table = $wpdb->prefix . 'dab_fields';

        // Create table with installation prefix
        $table_slug = $installation_name . '_' . $table_config['slug'];
        $table_label = $table_config['label'];

        // Check if table already exists
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $tables_table WHERE table_slug = %s",
            $table_slug
        ));

        if ($existing) {
            return new WP_Error('table_exists', sprintf(__('Table %s already exists', 'db-app-builder'), $table_slug));
        }

        // Insert table
        $wpdb->insert($tables_table, array(
            'table_label' => $table_label,
            'table_slug' => $table_slug,
            'description' => $table_config['description'] ?? '',
            'created_at' => current_time('mysql')
        ));

        $table_id = $wpdb->insert_id;

        // Create data table
        DAB_DB_Manager::create_data_table($table_slug);

        // Install fields
        if (isset($table_config['fields'])) {
            foreach ($table_config['fields'] as $index => $field_config) {
                $wpdb->insert($fields_table, array(
                    'table_id' => $table_id,
                    'field_label' => $field_config['label'],
                    'field_slug' => $field_config['slug'],
                    'field_type' => $field_config['type'],
                    'required' => $field_config['required'] ?? 0,
                    'placeholder' => $field_config['placeholder'] ?? '',
                    'field_options' => isset($field_config['options']) ? json_encode($field_config['options']) : '',
                    'field_order' => $index + 1,
                    'created_at' => current_time('mysql')
                ));

                // Create column in data table
                $column_type = DAB_DB_Manager::get_column_type_for_field($field_config['type']);
                DAB_DB_Manager::ensure_column_exists(
                    $wpdb->prefix . 'dab_' . $table_slug,
                    $field_config['slug'],
                    $column_type
                );
            }
        }

        return array(
            'table_id' => $table_id,
            'table_slug' => $table_slug,
            'table_label' => $table_label
        );
    }

    /**
     * Install template form
     */
    private static function install_template_form($form_config, $installed_tables) {
        global $wpdb;

        $forms_table = $wpdb->prefix . 'dab_forms';

        // Find the target table
        $target_table = null;
        foreach ($installed_tables as $table) {
            if ($table['table_slug'] === $form_config['table_slug'] ||
                str_ends_with($table['table_slug'], '_' . $form_config['table_slug'])) {
                $target_table = $table;
                break;
            }
        }

        if (!$target_table) {
            return new WP_Error('table_not_found', sprintf(__('Target table %s not found', 'db-app-builder'), $form_config['table_slug']));
        }

        // Insert form
        $wpdb->insert($forms_table, array(
            'form_name' => $form_config['name'],
            'table_id' => $target_table['table_id'],
            'fields' => json_encode($form_config['fields'] ?? array()),
            'notify_email' => $form_config['notify_email'] ?? '',
            'notify_message' => $form_config['notify_message'] ?? '',
            'created_at' => current_time('mysql')
        ));

        return array(
            'form_id' => $wpdb->insert_id,
            'form_name' => $form_config['name'],
            'table_id' => $target_table['table_id']
        );
    }

    /**
     * Install template view
     */
    private static function install_template_view($view_config, $installed_tables) {
        global $wpdb;

        $views_table = $wpdb->prefix . 'dab_views';

        // Find the target table
        $target_table = null;
        foreach ($installed_tables as $table) {
            if ($table['table_slug'] === $view_config['table_slug'] ||
                str_ends_with($table['table_slug'], '_' . $view_config['table_slug'])) {
                $target_table = $table;
                break;
            }
        }

        if (!$target_table) {
            return new WP_Error('table_not_found', sprintf(__('Target table %s not found', 'db-app-builder'), $view_config['table_slug']));
        }

        // Insert view
        $wpdb->insert($views_table, array(
            'view_name' => $view_config['name'],
            'table_id' => $target_table['table_id'],
            'columns' => json_encode($view_config['columns'] ?? array()),
            'filters' => json_encode($view_config['filters'] ?? array()),
            'sort_order' => $view_config['sort_order'] ?? '',
            'is_public' => $view_config['is_public'] ?? 0,
            'created_at' => current_time('mysql')
        ));

        return array(
            'view_id' => $wpdb->insert_id,
            'view_name' => $view_config['name'],
            'table_id' => $target_table['table_id']
        );
    }

    /**
     * Get template preview
     */
    public static function get_template_preview() {
        if (!wp_verify_nonce($_POST['nonce'], 'dab_templates_nonce')) {
            wp_send_json_error(__('Security check failed', 'db-app-builder'));
        }

        $template_id = intval($_POST['template_id']);

        global $wpdb;
        $templates_table = $wpdb->prefix . 'dab_app_templates';
        $template = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $templates_table WHERE id = %d AND is_active = 1",
            $template_id
        ));

        if (!$template) {
            wp_send_json_error(__('Template not found', 'db-app-builder'));
        }

        $template_config = json_decode($template->template_config, true);

        wp_send_json_success(array(
            'template' => $template,
            'config' => $template_config,
            'preview_html' => self::generate_preview_html($template, $template_config)
        ));
    }

    /**
     * Generate preview HTML
     */
    private static function generate_preview_html($template, $config) {
        ob_start();
        ?>
        <div class="dab-template-preview">
            <h3><?php echo esc_html($template->name); ?></h3>
            <p><?php echo esc_html($template->description); ?></p>

            <?php if (isset($config['tables'])): ?>
                <h4><?php _e('Tables', 'db-app-builder'); ?></h4>
                <ul class="dab-preview-list">
                    <?php foreach ($config['tables'] as $table): ?>
                        <li>
                            <strong><?php echo esc_html($table['label']); ?></strong>
                            <?php if (isset($table['fields'])): ?>
                                <span class="dab-field-count">(<?php echo count($table['fields']); ?> fields)</span>
                            <?php endif; ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>

            <?php if (isset($config['forms'])): ?>
                <h4><?php _e('Forms', 'db-app-builder'); ?></h4>
                <ul class="dab-preview-list">
                    <?php foreach ($config['forms'] as $form): ?>
                        <li><?php echo esc_html($form['name']); ?></li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>

            <?php if (isset($config['views'])): ?>
                <h4><?php _e('Views', 'db-app-builder'); ?></h4>
                <ul class="dab-preview-list">
                    <?php foreach ($config['views'] as $view): ?>
                        <li><?php echo esc_html($view['name']); ?></li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Search templates
     */
    public static function search_templates() {
        if (!wp_verify_nonce($_POST['nonce'], 'dab_templates_nonce')) {
            wp_send_json_error(__('Security check failed', 'db-app-builder'));
        }

        $search_term = sanitize_text_field($_POST['search'] ?? '');
        $category = sanitize_text_field($_POST['category'] ?? '');

        global $wpdb;
        $templates_table = $wpdb->prefix . 'dab_app_templates';

        $where_conditions = array('is_active = 1');
        $where_values = array();

        if (!empty($search_term)) {
            $where_conditions[] = '(name LIKE %s OR description LIKE %s)';
            $where_values[] = '%' . $wpdb->esc_like($search_term) . '%';
            $where_values[] = '%' . $wpdb->esc_like($search_term) . '%';
        }

        if (!empty($category)) {
            $where_conditions[] = 'category = %s';
            $where_values[] = $category;
        }

        $where_clause = implode(' AND ', $where_conditions);
        $query = "SELECT * FROM $templates_table WHERE $where_clause ORDER BY install_count DESC, name ASC";

        if (!empty($where_values)) {
            $templates = $wpdb->get_results($wpdb->prepare($query, $where_values));
        } else {
            $templates = $wpdb->get_results($query);
        }

        wp_send_json_success(array('templates' => $templates));
    }

    /**
     * Delete template installation
     */
    public static function delete_template_installation() {
        if (!wp_verify_nonce($_POST['nonce'], 'dab_templates_nonce')) {
            wp_send_json_error(__('Security check failed', 'db-app-builder'));
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'db-app-builder'));
        }

        $installation_id = intval($_POST['installation_id']);

        global $wpdb;
        $installations_table = $wpdb->prefix . 'dab_template_installations';

        // Get installation data
        $installation = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $installations_table WHERE id = %d",
            $installation_id
        ));

        if (!$installation) {
            wp_send_json_error(__('Installation not found', 'db-app-builder'));
        }

        // Delete related data (tables, forms, views)
        $installation_data = json_decode($installation->installation_data, true);

        if (isset($installation_data['tables'])) {
            foreach ($installation_data['tables'] as $table) {
                // Delete data table
                $wpdb->query($wpdb->prepare("DROP TABLE IF EXISTS %s", $wpdb->prefix . 'dab_' . $table['table_slug']));

                // Delete table record
                $wpdb->delete($wpdb->prefix . 'dab_tables', array('id' => $table['table_id']));

                // Delete fields
                $wpdb->delete($wpdb->prefix . 'dab_fields', array('table_id' => $table['table_id']));
            }
        }

        if (isset($installation_data['forms'])) {
            foreach ($installation_data['forms'] as $form) {
                $wpdb->delete($wpdb->prefix . 'dab_forms', array('id' => $form['form_id']));
            }
        }

        if (isset($installation_data['views'])) {
            foreach ($installation_data['views'] as $view) {
                $wpdb->delete($wpdb->prefix . 'dab_views', array('id' => $view['view_id']));
            }
        }

        // Delete installation record
        $wpdb->delete($installations_table, array('id' => $installation_id));

        wp_send_json_success(array('message' => __('Template installation deleted successfully', 'db-app-builder')));
    }

    /**
     * Insert default templates
     */
    private static function insert_default_templates() {
        global $wpdb;
        $templates_table = $wpdb->prefix . 'dab_app_templates';

        $default_templates = array(
            // Customer & Sales Management
            array(
                'template_key' => 'crm_basic',
                'name' => __('Customer Relationship Management (CRM)', 'db-app-builder'),
                'description' => __('Complete CRM system with customers, contacts, deals, and interactions tracking.', 'db-app-builder'),
                'category' => 'customer_sales',
                'subcategory' => 'crm',
                'icon' => 'dashicons-groups',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'customers',
                            'label' => 'Customers',
                            'description' => 'Customer information and details',
                            'fields' => array(
                                array('slug' => 'company_name', 'label' => 'Company Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'contact_person', 'label' => 'Contact Person', 'type' => 'text', 'required' => 1),
                                array('slug' => 'email', 'label' => 'Email', 'type' => 'email', 'required' => 1),
                                array('slug' => 'phone', 'label' => 'Phone', 'type' => 'text', 'required' => 0),
                                array('slug' => 'address', 'label' => 'Address', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'industry', 'label' => 'Industry', 'type' => 'select', 'required' => 0, 'options' => array('Technology', 'Healthcare', 'Finance', 'Manufacturing', 'Retail', 'Other')),
                                array('slug' => 'customer_status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Lead', 'Prospect', 'Customer', 'Inactive')),
                                array('slug' => 'notes', 'label' => 'Notes', 'type' => 'textarea', 'required' => 0)
                            )
                        ),
                        array(
                            'slug' => 'deals',
                            'label' => 'Deals',
                            'description' => 'Sales opportunities and deals',
                            'fields' => array(
                                array('slug' => 'deal_name', 'label' => 'Deal Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'customer_id', 'label' => 'Customer', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'deal_value', 'label' => 'Deal Value', 'type' => 'number', 'required' => 1),
                                array('slug' => 'deal_stage', 'label' => 'Stage', 'type' => 'select', 'required' => 1, 'options' => array('Prospecting', 'Qualification', 'Proposal', 'Negotiation', 'Closed Won', 'Closed Lost')),
                                array('slug' => 'probability', 'label' => 'Probability (%)', 'type' => 'number', 'required' => 0),
                                array('slug' => 'expected_close_date', 'label' => 'Expected Close Date', 'type' => 'date', 'required' => 0),
                                array('slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'required' => 0)
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Add Customer',
                            'table_slug' => 'customers',
                            'fields' => array('company_name', 'contact_person', 'email', 'phone', 'address', 'industry', 'customer_status', 'notes')
                        ),
                        array(
                            'name' => 'Add Deal',
                            'table_slug' => 'deals',
                            'fields' => array('deal_name', 'customer_id', 'deal_value', 'deal_stage', 'probability', 'expected_close_date', 'description')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'All Customers',
                            'table_slug' => 'customers',
                            'columns' => array('company_name', 'contact_person', 'email', 'customer_status'),
                            'sort_order' => 'company_name ASC'
                        ),
                        array(
                            'name' => 'Active Deals',
                            'table_slug' => 'deals',
                            'columns' => array('deal_name', 'deal_value', 'deal_stage', 'probability', 'expected_close_date'),
                            'filters' => array(array('field' => 'deal_stage', 'operator' => 'NOT IN', 'value' => 'Closed Won,Closed Lost')),
                            'sort_order' => 'expected_close_date ASC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Project Management
            array(
                'template_key' => 'project_management',
                'name' => __('Project Management System', 'db-app-builder'),
                'description' => __('Complete project management with projects, tasks, milestones, and team collaboration.', 'db-app-builder'),
                'category' => 'project_task',
                'subcategory' => 'project_management',
                'icon' => 'dashicons-calendar-alt',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'projects',
                            'label' => 'Projects',
                            'description' => 'Project information and tracking',
                            'fields' => array(
                                array('slug' => 'project_name', 'label' => 'Project Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'project_manager', 'label' => 'Project Manager', 'type' => 'text', 'required' => 1),
                                array('slug' => 'start_date', 'label' => 'Start Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'end_date', 'label' => 'End Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'budget', 'label' => 'Budget', 'type' => 'number', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Planning', 'In Progress', 'On Hold', 'Completed', 'Cancelled')),
                                array('slug' => 'priority', 'label' => 'Priority', 'type' => 'select', 'required' => 1, 'options' => array('Low', 'Medium', 'High', 'Critical'))
                            )
                        ),
                        array(
                            'slug' => 'tasks',
                            'label' => 'Tasks',
                            'description' => 'Project tasks and assignments',
                            'fields' => array(
                                array('slug' => 'task_name', 'label' => 'Task Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'project_id', 'label' => 'Project', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'assigned_to', 'label' => 'Assigned To', 'type' => 'text', 'required' => 0),
                                array('slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'due_date', 'label' => 'Due Date', 'type' => 'date', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Not Started', 'In Progress', 'Completed', 'Blocked')),
                                array('slug' => 'priority', 'label' => 'Priority', 'type' => 'select', 'required' => 1, 'options' => array('Low', 'Medium', 'High', 'Critical')),
                                array('slug' => 'estimated_hours', 'label' => 'Estimated Hours', 'type' => 'number', 'required' => 0)
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Create Project',
                            'table_slug' => 'projects',
                            'fields' => array('project_name', 'description', 'project_manager', 'start_date', 'end_date', 'budget', 'status', 'priority')
                        ),
                        array(
                            'name' => 'Add Task',
                            'table_slug' => 'tasks',
                            'fields' => array('task_name', 'project_id', 'assigned_to', 'description', 'due_date', 'status', 'priority', 'estimated_hours')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'Active Projects',
                            'table_slug' => 'projects',
                            'columns' => array('project_name', 'project_manager', 'start_date', 'end_date', 'status', 'priority'),
                            'filters' => array(array('field' => 'status', 'operator' => 'NOT IN', 'value' => 'Completed,Cancelled')),
                            'sort_order' => 'start_date ASC'
                        ),
                        array(
                            'name' => 'My Tasks',
                            'table_slug' => 'tasks',
                            'columns' => array('task_name', 'due_date', 'status', 'priority'),
                            'filters' => array(array('field' => 'status', 'operator' => '!=', 'value' => 'Completed')),
                            'sort_order' => 'due_date ASC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Employee Management
            array(
                'template_key' => 'employee_management',
                'name' => __('Employee Management System', 'db-app-builder'),
                'description' => __('Comprehensive HR system for managing employees, departments, and basic HR functions.', 'db-app-builder'),
                'category' => 'human_resources',
                'subcategory' => 'employee_management',
                'icon' => 'dashicons-admin-users',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'employees',
                            'label' => 'Employees',
                            'description' => 'Employee information and records',
                            'fields' => array(
                                array('slug' => 'employee_id', 'label' => 'Employee ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'first_name', 'label' => 'First Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'last_name', 'label' => 'Last Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'email', 'label' => 'Email', 'type' => 'email', 'required' => 1),
                                array('slug' => 'phone', 'label' => 'Phone', 'type' => 'text', 'required' => 0),
                                array('slug' => 'department', 'label' => 'Department', 'type' => 'select', 'required' => 1, 'options' => array('HR', 'IT', 'Finance', 'Marketing', 'Sales', 'Operations')),
                                array('slug' => 'position', 'label' => 'Position', 'type' => 'text', 'required' => 1),
                                array('slug' => 'hire_date', 'label' => 'Hire Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'salary', 'label' => 'Salary', 'type' => 'number', 'required' => 0),
                                array('slug' => 'employment_status', 'label' => 'Employment Status', 'type' => 'select', 'required' => 1, 'options' => array('Active', 'Inactive', 'Terminated', 'On Leave'))
                            )
                        ),
                        array(
                            'slug' => 'leave_requests',
                            'label' => 'Leave Requests',
                            'description' => 'Employee leave and vacation requests',
                            'fields' => array(
                                array('slug' => 'employee_id', 'label' => 'Employee', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'leave_type', 'label' => 'Leave Type', 'type' => 'select', 'required' => 1, 'options' => array('Vacation', 'Sick Leave', 'Personal Leave', 'Maternity/Paternity', 'Emergency')),
                                array('slug' => 'start_date', 'label' => 'Start Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'end_date', 'label' => 'End Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'days_requested', 'label' => 'Days Requested', 'type' => 'number', 'required' => 1),
                                array('slug' => 'reason', 'label' => 'Reason', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Pending', 'Approved', 'Rejected')),
                                array('slug' => 'approved_by', 'label' => 'Approved By', 'type' => 'text', 'required' => 0)
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Add Employee',
                            'table_slug' => 'employees',
                            'fields' => array('employee_id', 'first_name', 'last_name', 'email', 'phone', 'department', 'position', 'hire_date', 'salary', 'employment_status')
                        ),
                        array(
                            'name' => 'Leave Request',
                            'table_slug' => 'leave_requests',
                            'fields' => array('employee_id', 'leave_type', 'start_date', 'end_date', 'days_requested', 'reason')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'All Employees',
                            'table_slug' => 'employees',
                            'columns' => array('employee_id', 'first_name', 'last_name', 'department', 'position', 'employment_status'),
                            'sort_order' => 'last_name ASC'
                        ),
                        array(
                            'name' => 'Pending Leave Requests',
                            'table_slug' => 'leave_requests',
                            'columns' => array('employee_id', 'leave_type', 'start_date', 'end_date', 'days_requested', 'status'),
                            'filters' => array(array('field' => 'status', 'operator' => '=', 'value' => 'Pending')),
                            'sort_order' => 'start_date ASC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Inventory Management
            array(
                'template_key' => 'inventory_management',
                'name' => __('Inventory Management System', 'db-app-builder'),
                'description' => __('Complete inventory tracking with products, stock levels, suppliers, and purchase orders.', 'db-app-builder'),
                'category' => 'inventory_operations',
                'subcategory' => 'inventory',
                'icon' => 'dashicons-archive',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'products',
                            'label' => 'Products',
                            'description' => 'Product catalog and information',
                            'fields' => array(
                                array('slug' => 'product_code', 'label' => 'Product Code', 'type' => 'text', 'required' => 1),
                                array('slug' => 'product_name', 'label' => 'Product Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'category', 'label' => 'Category', 'type' => 'select', 'required' => 1, 'options' => array('Electronics', 'Clothing', 'Books', 'Home & Garden', 'Sports', 'Other')),
                                array('slug' => 'unit_price', 'label' => 'Unit Price', 'type' => 'number', 'required' => 1),
                                array('slug' => 'cost_price', 'label' => 'Cost Price', 'type' => 'number', 'required' => 0),
                                array('slug' => 'current_stock', 'label' => 'Current Stock', 'type' => 'number', 'required' => 1),
                                array('slug' => 'minimum_stock', 'label' => 'Minimum Stock Level', 'type' => 'number', 'required' => 1),
                                array('slug' => 'supplier', 'label' => 'Supplier', 'type' => 'text', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Active', 'Inactive', 'Discontinued'))
                            )
                        ),
                        array(
                            'slug' => 'stock_movements',
                            'label' => 'Stock Movements',
                            'description' => 'Track stock in and out movements',
                            'fields' => array(
                                array('slug' => 'product_id', 'label' => 'Product', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'movement_type', 'label' => 'Movement Type', 'type' => 'select', 'required' => 1, 'options' => array('Stock In', 'Stock Out', 'Adjustment', 'Transfer')),
                                array('slug' => 'quantity', 'label' => 'Quantity', 'type' => 'number', 'required' => 1),
                                array('slug' => 'reference', 'label' => 'Reference', 'type' => 'text', 'required' => 0),
                                array('slug' => 'notes', 'label' => 'Notes', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'movement_date', 'label' => 'Movement Date', 'type' => 'date', 'required' => 1)
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Add Product',
                            'table_slug' => 'products',
                            'fields' => array('product_code', 'product_name', 'description', 'category', 'unit_price', 'cost_price', 'current_stock', 'minimum_stock', 'supplier', 'status')
                        ),
                        array(
                            'name' => 'Stock Movement',
                            'table_slug' => 'stock_movements',
                            'fields' => array('product_id', 'movement_type', 'quantity', 'reference', 'notes', 'movement_date')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'All Products',
                            'table_slug' => 'products',
                            'columns' => array('product_code', 'product_name', 'category', 'current_stock', 'minimum_stock', 'status'),
                            'sort_order' => 'product_name ASC'
                        ),
                        array(
                            'name' => 'Low Stock Alert',
                            'table_slug' => 'products',
                            'columns' => array('product_code', 'product_name', 'current_stock', 'minimum_stock'),
                            'filters' => array(array('field' => 'current_stock', 'operator' => '<=', 'value' => 'minimum_stock')),
                            'sort_order' => 'current_stock ASC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Event Management
            array(
                'template_key' => 'event_management',
                'name' => __('Event Management System', 'db-app-builder'),
                'description' => __('Comprehensive event planning with events, attendees, venues, and registration management.', 'db-app-builder'),
                'category' => 'event_hospitality',
                'subcategory' => 'event_planning',
                'icon' => 'dashicons-calendar',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'events',
                            'label' => 'Events',
                            'description' => 'Event information and details',
                            'fields' => array(
                                array('slug' => 'event_name', 'label' => 'Event Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'event_type', 'label' => 'Event Type', 'type' => 'select', 'required' => 1, 'options' => array('Conference', 'Workshop', 'Seminar', 'Meeting', 'Social', 'Training', 'Other')),
                                array('slug' => 'start_date', 'label' => 'Start Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'end_date', 'label' => 'End Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'start_time', 'label' => 'Start Time', 'type' => 'time', 'required' => 1),
                                array('slug' => 'end_time', 'label' => 'End Time', 'type' => 'time', 'required' => 1),
                                array('slug' => 'venue', 'label' => 'Venue', 'type' => 'text', 'required' => 1),
                                array('slug' => 'max_attendees', 'label' => 'Maximum Attendees', 'type' => 'number', 'required' => 0),
                                array('slug' => 'registration_fee', 'label' => 'Registration Fee', 'type' => 'number', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Planning', 'Open for Registration', 'Registration Closed', 'In Progress', 'Completed', 'Cancelled'))
                            )
                        ),
                        array(
                            'slug' => 'registrations',
                            'label' => 'Event Registrations',
                            'description' => 'Event attendee registrations',
                            'fields' => array(
                                array('slug' => 'event_id', 'label' => 'Event', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'attendee_name', 'label' => 'Attendee Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'email', 'label' => 'Email', 'type' => 'email', 'required' => 1),
                                array('slug' => 'phone', 'label' => 'Phone', 'type' => 'text', 'required' => 0),
                                array('slug' => 'organization', 'label' => 'Organization', 'type' => 'text', 'required' => 0),
                                array('slug' => 'dietary_requirements', 'label' => 'Dietary Requirements', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'registration_status', 'label' => 'Registration Status', 'type' => 'select', 'required' => 1, 'options' => array('Registered', 'Confirmed', 'Attended', 'No Show', 'Cancelled')),
                                array('slug' => 'payment_status', 'label' => 'Payment Status', 'type' => 'select', 'required' => 1, 'options' => array('Pending', 'Paid', 'Refunded', 'Waived'))
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Create Event',
                            'table_slug' => 'events',
                            'fields' => array('event_name', 'description', 'event_type', 'start_date', 'end_date', 'start_time', 'end_time', 'venue', 'max_attendees', 'registration_fee', 'status')
                        ),
                        array(
                            'name' => 'Event Registration',
                            'table_slug' => 'registrations',
                            'fields' => array('event_id', 'attendee_name', 'email', 'phone', 'organization', 'dietary_requirements')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'Upcoming Events',
                            'table_slug' => 'events',
                            'columns' => array('event_name', 'event_type', 'start_date', 'venue', 'status'),
                            'filters' => array(array('field' => 'start_date', 'operator' => '>=', 'value' => 'TODAY()')),
                            'sort_order' => 'start_date ASC'
                        ),
                        array(
                            'name' => 'Event Registrations',
                            'table_slug' => 'registrations',
                            'columns' => array('event_id', 'attendee_name', 'email', 'registration_status', 'payment_status'),
                            'sort_order' => 'created_at DESC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Expense Management
            array(
                'template_key' => 'expense_management',
                'name' => __('Expense Management System', 'db-app-builder'),
                'description' => __('Track and manage business expenses with categories, approvals, and reporting.', 'db-app-builder'),
                'category' => 'financial',
                'subcategory' => 'expense_management',
                'icon' => 'dashicons-money-alt',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'expenses',
                            'label' => 'Expenses',
                            'description' => 'Business expense records',
                            'fields' => array(
                                array('slug' => 'expense_title', 'label' => 'Expense Title', 'type' => 'text', 'required' => 1),
                                array('slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'amount', 'label' => 'Amount', 'type' => 'number', 'required' => 1),
                                array('slug' => 'currency', 'label' => 'Currency', 'type' => 'select', 'required' => 1, 'options' => array('USD', 'EUR', 'GBP', 'CAD', 'AUD')),
                                array('slug' => 'category', 'label' => 'Category', 'type' => 'select', 'required' => 1, 'options' => array('Travel', 'Meals', 'Office Supplies', 'Software', 'Marketing', 'Training', 'Other')),
                                array('slug' => 'expense_date', 'label' => 'Expense Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'vendor', 'label' => 'Vendor/Merchant', 'type' => 'text', 'required' => 0),
                                array('slug' => 'payment_method', 'label' => 'Payment Method', 'type' => 'select', 'required' => 1, 'options' => array('Cash', 'Credit Card', 'Debit Card', 'Bank Transfer', 'Check')),
                                array('slug' => 'receipt_number', 'label' => 'Receipt Number', 'type' => 'text', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Draft', 'Submitted', 'Approved', 'Rejected', 'Reimbursed')),
                                array('slug' => 'submitted_by', 'label' => 'Submitted By', 'type' => 'text', 'required' => 1)
                            )
                        ),
                        array(
                            'slug' => 'expense_approvals',
                            'label' => 'Expense Approvals',
                            'description' => 'Expense approval workflow',
                            'fields' => array(
                                array('slug' => 'expense_id', 'label' => 'Expense', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'approver', 'label' => 'Approver', 'type' => 'text', 'required' => 1),
                                array('slug' => 'approval_status', 'label' => 'Approval Status', 'type' => 'select', 'required' => 1, 'options' => array('Pending', 'Approved', 'Rejected')),
                                array('slug' => 'approval_date', 'label' => 'Approval Date', 'type' => 'date', 'required' => 0),
                                array('slug' => 'comments', 'label' => 'Comments', 'type' => 'textarea', 'required' => 0)
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Submit Expense',
                            'table_slug' => 'expenses',
                            'fields' => array('expense_title', 'description', 'amount', 'currency', 'category', 'expense_date', 'vendor', 'payment_method', 'receipt_number', 'submitted_by')
                        ),
                        array(
                            'name' => 'Expense Approval',
                            'table_slug' => 'expense_approvals',
                            'fields' => array('expense_id', 'approver', 'approval_status', 'approval_date', 'comments')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'All Expenses',
                            'table_slug' => 'expenses',
                            'columns' => array('expense_title', 'amount', 'category', 'expense_date', 'status', 'submitted_by'),
                            'sort_order' => 'expense_date DESC'
                        ),
                        array(
                            'name' => 'Pending Approvals',
                            'table_slug' => 'expenses',
                            'columns' => array('expense_title', 'amount', 'category', 'submitted_by', 'expense_date'),
                            'filters' => array(array('field' => 'status', 'operator' => '=', 'value' => 'Submitted')),
                            'sort_order' => 'expense_date ASC'
                        )
                    )
                )),
                'is_system' => 1
            )
        );

        foreach ($default_templates as $template) {
            $existing = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $templates_table WHERE template_key = %s",
                $template['template_key']
            ));

            if (!$existing) {
                $wpdb->insert($templates_table, $template);
            }
        }
    }
}

// Initialize the Template Manager
DAB_Template_Manager::init();
